<div class="m-customer-portal" data-js="customer-portal-container">
    <header class="c-header">
        <div class="c-h-logo">
            <img src="{{company_logo}}" alt="Company Logo">
        </div>
        <div class="c-h-navigation" data-js="tab-menu">
            <div class="c-hn-extra-navigation">
                <div class="c-hn-user-profile">
                    {{> ../components/partials/user-profile-pill firstName=firstName}}
                </div>
            </div>
            <div class="c-hn-main-navigation">
                {{#each tab_items}}
                    {{#if is_enabled}}
                        <div class="c-hnm-item{{#if @first}} t-active{{/if}}" data-js="tab-item" data-id="{{@key}}">
                            <svg class="c-hnmi-icon"><use xlink:href="#remix-icon--{{icon}}"></use></svg>
                            <a class="c-hnmi-title">{{title}}</a>
                        </div>
                    {{/if}}
                {{/each}}
                <div class="c-hn-user-profile">
                    {{> ../components/partials/user-profile-pill firstName=firstName}}
                </div>
            </div>
        </div>

    </header>

    <div class="c-container-scrollable">

        <div class="c-cs-tab-sections">

            {{> ../components/company-info company=company salesperson=salesperson}}
            <div class="m-pages" data-js="page-container"></div>
        </div>
    </div>

    {{! Mobile company info button }}
    <button class="c-mobile-info-button t-hidden" data-js="mobile-company-info-button" type="button" aria-label="Company Information">
        <svg><use xlink:href="#remix-icon--system--information-line"></use></svg>
    </button>
</div>