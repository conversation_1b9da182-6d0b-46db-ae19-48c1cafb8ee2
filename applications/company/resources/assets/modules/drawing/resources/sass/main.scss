@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/grid';
@use '~@cac-sass/app/button/text';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/form';
@use '~@cac-sass/app/form/inline';
@use '~@cas-layout-sass/layout';
@use '~@cas-accordion-sass/accordion';
@use '~@cas-form-input-sass/button-group';
@use '~@cas-form-input-sass/color';
@use '~@cas-form-input-sass/checkbox';
@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cas-form-input-sass/number_spinner';
@use '~@cas-list-sass/list';
@use '~@cas-table-sass/table';
@use '~@cas-modal-sass/modal';
@use '~@cas-flash-message-sass/config' as flash-message-config;
@use 'sass:color' as sass-color;
@use 'config';

html {
    -webkit-tap-highlight-color: transparent;
}
body {
    &.no-overscroll {
        position: fixed;
        width: 100%;
    }
}

.m-drawing {
    @include base.full-width-height;
    user-select: none;
    .c-d-loader {
        display: none;
        position: absolute;
        @include base.full-width-height;
        background: rgba(0, 0, 0, 0.5) url('~@cac-public/images/loading.svg') no-repeat center;
        background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
        z-index: 120;
    }
    .c-d-panels {
        position: relative;
        @include base.full-width-height;
        overflow: hidden;
        z-index: 1;
    }
        .c-dp-panel {
            position: absolute;
            @include base.full-width-height;
            top: 100%;
            transition: top 1s ease;
            touch-action: none;
            &.t-active {
                top: 0;
            }
            &.t-main {
                position: static;
                top: 0;
                transition: none;
            }
            &.t-no-animate {
                transition: none;
            }
            &.t-top {
                z-index: 2;
            }
            &.t-bottom {
                z-index: 1;
            }
        }
}

.m-panel-manager {
    padding: base.unit-rem-calc(24px);
    @include base.respond-to('<small') {
        padding: 0;
    }
    .c-pm-flash-messages {
        &:has(.s-flash-messages.t-has-message){
            padding-bottom: base.unit-rem-calc(16px);
            @include base.respond-to('<small') {
                padding: base.unit-rem-calc(8px) base.unit-rem-calc(8px) 0;
            }
        }
    }
    .c-pm-wrapper {
        @include base.full-width-height;
        padding: base.unit-rem-calc(12px);
        background-color: base.$color-white-default;
        border-radius: base.unit-rem-calc(12px);
        box-shadow: base.$elevation-level-1;
        @include base.respond-to('<small') {
            padding: base.unit-rem-calc(12px) 0;
            border-radius: 0;
            box-shadow: none;
        }
    }
        .c-pmw-header {
            display: flex;
            align-items: center;
            width: 100%;
        }
            .c-pmwh-text {
                flex: 1;
                font-size: base.unit-rem-calc(22px);
            }
            .c-pmwh-actions {
                flex: 0 0 auto;
                display: flex;
            }
                .c-pmwha-action {
                    flex: 0 0 auto;
                    margin-right: base.unit-rem-calc(10px);
                    &:last-child {
                        margin-right: 0;
                    }
                }
        .c-pmw-content {

        }
}

.m-importer {
    display: none;
    position: fixed;
    top: base.unit-rem-calc(50px);
    left: base.unit-rem-calc(50px);
    width: base.unit-rem-calc(250px);
    border: base.unit-rem-calc(1px) #000 solid;
    padding: base.unit-rem-calc(10px);
    background-color: base.$color-white-default;
    z-index: 1000;
}

.m-zoom-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @include base.full-width-height;
    background-color: rgba(0, 0, 0, 0.5);
    background-size: base.unit-rem-calc(200px) base.unit-rem-calc(200px);
    z-index: 3000;
    .c-zo-inner {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: absolute;
        width: base.unit-rem-calc(300px);
    }
        .c-zoi-icon {
            @include base.svg-icon-base($absolute: false);
            width: base.unit-rem-calc(100px);
            height: base.unit-rem-calc(100px);
            color: base.$color-white-default;
        }
        .c-zoi-text {
            width: 100%;
            margin-top: base.unit-rem-calc(12px);
            font-size: base.unit-rem-calc(18px);
            color: base.$color-white-default;
            text-align: center;
        }
}

$drawing-header-height: base.unit-rem-calc(40px);
$border-width: base.unit-rem-calc(1px);
$top-bar-height: base.unit-rem-calc(51px);
.m-panel-drawing {
    position: relative;
    @include base.full-width-height;
    overflow: hidden;
    background-color: grey;
    padding-top: $top-bar-height;
    .c-pd-messages {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 120;
        .i-fm-message {
            height: $top-bar-height;
            border-bottom: base.unit-rem-calc(1px) sass-color.adjust(flash-message-config.$error-bg-color, $lightness: -15%) solid;
        }
    }
    $header-height: base.unit-rem-calc(51px);
    .c-pd-header {
        display: flex;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: $header-height;
        border-bottom: $border-width base.$color-border solid;
        box-shadow: 2px 6px 10px 0 rgba(170, 170, 170, 0.17);
        background-color: base.$color-white-default;
        z-index: 115;
    }
        .c-pdh-back {
            display: block;
            flex: 0 0 auto;
            position: relative;
            width: base.unit-rem-calc(50px);
            height: $header-height;
            &:hover, &:active {
                .c-pdhb-icon {
                    color: base.$color-dark-grey;
                }
            }
        }
            .c-pdhb-icon {
                @include base.svg-icon-base;
                top: base.unit-rem-calc(17px);
                left: base.unit-rem-calc(20px);
                width: base.unit-rem-calc(10px);
                height: base.unit-rem-calc(16px);
                color: base.$color-medium-grey;
            }
        .c-pdh-content {
            display: flex;
            align-items: center;
            flex: 1;
            height: $header-height;
        }
            .c-pdhc-details {
                display: flex;
                align-items: center;
                flex: 1;
            }
                .c-pdhcd-name {
                    margin-right: base.unit-rem-calc(5px);
                    font-size: base.unit-rem-calc(15px);
                    font-weight: bold;
                    color: base.$color-dark-grey;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .c-pdhcd-edit {
                    display: block;
                    position: relative;
                    width: base.unit-rem-calc(30px);
                    height: base.unit-rem-calc(30px);
                }
                    .c-pdhcde-icon {
                        @include base.svg-icon-base;
                        top: base.unit-rem-calc(5px);
                        left: base.unit-rem-calc(5px);
                        width: base.unit-rem-calc(20px);
                        height: base.unit-rem-calc(20px);
                    }
                .c-pdhcd-save-status {
                    display: none;
                    margin-left: base.unit-rem-calc(15px);
                    font-size: base.unit-rem-calc(12px);
                    font-weight: normal;
                    color: base.$color-medium-grey;
                    letter-spacing: normal;
                    text-transform: none;
                }
            .c-pdhc-actions {
                display: flex;
                flex: 0 0 auto;
                margin-right: base.unit-rem-calc(20px);
            }
                .c-pdhca-action {
                    margin-right: base.unit-rem-calc(10px);
                    &:last-child {
                        margin-right: 0;
                    }
                }
    .c-pd-content {
        display: flex;
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
        $size-height: base.unit-rem-calc(30px);
        $button-height: base.unit-rem-calc(51px);
        .c-pdc-sidebar {
            position: relative;
            width: base.unit-rem-calc(150px);
            flex: 0 0 auto;
            box-shadow: 2px 0 10px 0 rgba(170, 170, 170, 0.17);
            padding: $size-height + $button-height 0 $button-height;
            background-color: base.$color-white-default;
            z-index: 116;
        }
            .c-pdcs-bottom {
                position: absolute;
                bottom: 0;
                left: 0;
                border-top: $border-width base.$color-border solid;
                box-shadow: 2px 0 10px 0 rgba(170, 170, 170, 0.17);
                z-index: 120;
            }
            .c-pdcs-top {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                border-bottom: $border-width base.$color-border solid;
                z-index: 120;
            }
                .c-pdcst-size {
                    width: 100%;
                    height: $size-height;
                    font-size: base.unit-rem-calc(14px);
                    line-height: $size-height;
                    text-align: center;
                    border-bottom: $border-width base.$color-border solid;
                }
                .c-pdcst-quick-access {
                    .c-pdcs--bg-action {
                        &.t-active {
                            background-color: config.$quick-access-button-active-bg-color;
                        }
                    }
                        .c-pdcs--bga-icon {
                            color: #000000 !important;
                        }
                }
            .c-pdcs--button-group {
                display: flex;
                width: 100%;
                height: $button-height;
            }
                .c-pdcs--bg-action {
                    display: block;
                    flex: 1;
                    position: relative;
                    height: 100%;
                    border-right: $border-width base.$color-border solid;
                    &:last-child {
                        border-right: none;
                    }
                    &.t-active {
                        .c-pdcs--bga-icon {
                            color: base.$color-dark-grey;
                        }
                    }
                    &.t-actions {
                        .c-pdcs--bga-icon {
                            top: base.unit-rem-calc(12px);
                            width: base.unit-rem-calc(25px);
                            height: base.unit-rem-calc(25px);
                        }
                    }
                    &.t-tool {
                        .c-pdcs--bga-icon {
                            top: base.unit-rem-calc(12px);
                            width: base.unit-rem-calc(25px);
                            height: base.unit-rem-calc(25px);
                        }
                    }
                    &:hover, &:active {
                        .c-pdcs--bga-icon {
                            color: base.$color-dark-grey;
                        }
                    }
                }
                    .c-pdcs--bga-icon {
                        @include base.svg-icon-base;
                        left: 50%;
                        transform: translateX(-50%);
                        color: base.$color-medium-grey;
                    }
            .c-pdcs-tools {
                width: 100%;
                height: 100%;
                overflow-y: auto;
            }
            .c-pdcs-actions {
                display: none;
                background: base.$color-white-default;
                border: 1px solid base.$color-border;
                box-shadow: 1px 1px 6px 0 rgba(170,170,170,0.90);
                border-radius: base.unit-rem-calc(2px);
                z-index: 121;
            }
                .c-pdcsa-action {
                    display: block;
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(16px);
                    border-bottom: $border-width base.$color-border solid;
                    color: config.$sidebar-action-default-text-color;
                    background-color: config.$sidebar-action-default-bg-color;
                    font-size: base.unit-rem-calc(14px);
                    &:last-child {
                        border-bottom: none;
                    }
                    &:hover {
                        color: config.$sidebar-action-hover-text-color;
                        background-color: config.$sidebar-action-hover-bg-color;
                    }
                }
        .c-pdc-paper {
            position: relative;
            flex: 1;
            height: 100%;
            overflow: hidden;
        }
            .c-pdcp-loader {
                display: none;
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5) url('~@cac-public/images/loading.svg') no-repeat center;
                background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
                z-index: 120;
            }
            .c-pdcp-canvas {
                display: block;
                position: relative;
                width: 100%;
                height: 100%;
                touch-action: none;
                user-select: none;
                z-index: 100;
            }
        .c-pdc-config-panel {
            display: none;
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 101;
        }
}

.m-popper {
    position: absolute;
    z-index: 102;
    &.t-line-length,
    &.t-rectangle-size {
        margin: base.unit-rem-calc(18px);
        padding: base.unit-rem-calc(2px) base.unit-rem-calc(4px);
        font-size: base.unit-rem-calc(11px);
        background-color: base.$color-lighter-grey;
        border: base.$color-border base.unit-rem-calc(1px) solid;
        border-radius: base.unit-rem-calc(2px);
        pointer-events: none;
    }
}

.m-config-panel {
    background: base.$color-white-default;
    border: 1px solid base.$color-border;
    box-shadow: 1px 1px 6px 0 rgba(170,170,170,0.90);
    border-radius: base.unit-rem-calc(2px) 0 0 0;
    &.t-line {
        width: base.unit-rem-calc(275px);
    }
    &.t-rectangle {
        width: base.unit-rem-calc(250px);
    }
    > .c-cp-name {
        font-size: base.unit-rem-calc(14px);
        font-weight: 500;
        letter-spacing: base.unit-rem-calc(1px);
        text-transform: uppercase;
        padding: base.unit-rem-calc(8px);
        background-color: base.$color-lighter-grey;
    }
    > .c-cp-content {
        padding: base.unit-rem-calc(10px);
        border-bottom: base.unit-rem-calc(2px) base.$color-border solid;
    }
    > .c-cp-actions {
        padding: base.unit-rem-calc(10px);
        display: flex;
        justify-content: space-evenly;
    }
        .c-cpa-action {
            margin-right: base.unit-rem-calc(16px);
            &:last-child {
                margin-right: 0;
            }
        }
    // submodules
    .m-line-config-panel {
        .c-lcp-option {
            margin-top: base.unit-rem-calc(8px);
            &:first-child {
                margin-top: 0;
            }
            &.t-style-color,
            &.t-dimensions {
                display: flex;
                align-items: center;
            }
        }
            .c-lcpo-style {
                flex: 1;
                margin-right: base.unit-rem-calc(16px);
            }
            .c-lcpo-color {
                flex: 0 0 auto;
            }
            .c-lcpo-dimension {
                flex: 1;
                margin-right: base.unit-rem-calc(16px);
            }
        .c-lcp-header {
            font-size: base.unit-rem-calc(14px);
            font-weight: 500;
            margin: base.unit-rem-calc(8px) 0 0;
            &:first-child {
                margin: 0;
            }
        }
    }
    .m-pier-config-panel {
        .c-pcp-number {
            width: base.unit-rem-calc(130px);
        }
    }
    .m-rectangle-config-panel {
        .c-rcp-option {
            margin-top: base.unit-rem-calc(16px);
            &.t-colors {
                display: flex;
                align-items: center;
            }
            &:first-child {
                margin-top: 0;
            }
        }
            .c-rcpo-border {
                flex: 1;
                margin-right: base.unit-rem-calc(16px);
            }
            .c-rcpo-background {
                flex: 1;
            }
    }
    .m-line-opening-config-panel {
        .c-locp-option {
            margin-top: base.unit-rem-calc(8px);
            &:first-child {
                margin-top: 0;
            }
        }
            .c-locpo--measurements {
                &.t-error {
                    .f-f-input {
                        border-color: base.$color-red;
                    }
                }
            }
            .c-locpo--error {
                display: none;
                padding-top: base.unit-rem-calc(6px);
                font-size: base.unit-rem-calc(12px);
                color: base.$color-red;
            }
    }
    .m-well-config-panel {
        .c-wcp-option {
            margin-top: base.unit-rem-calc(8px);
            &:first-child {
                margin-top: 0;
            }
        }
        .c-wcpo--measurements {
            &.t-error {
                .f-f-input {
                    border-color: base.$color-red;
                }
            }
        }
        .c-wcpo--error {
            display: none;
            padding-top: base.unit-rem-calc(6px);
            font-size: base.unit-rem-calc(12px);
            color: base.$color-red;
        }
    }
    .m-length-config-panel {
        .c-lcp-option {
            margin-top: base.unit-rem-calc(8px);
            &:first-child {
                margin-top: 0;
            }
        }
        .c-lcpo--measurements {
            &.t-error {
                .f-f-input {
                    border-color: base.$color-red;
                }
            }
        }
        .c-lcpo--error {
            display: none;
            padding-top: base.unit-rem-calc(6px);
            font-size: base.unit-rem-calc(12px);
            color: base.$color-red;
        }
    }
    .m-wall-crack-config-panel {
        width: base.unit-rem-calc(225px);
        .c-wccp-option {
            margin-bottom: base.unit-rem-calc(8px);
            &:last-child {
                margin-bottom: 0;
            }
        }
            .c-wccpo--label {
                width: base.unit-rem-calc(45px);
                text-align: right;
            }
            .c-wccpo--measurements {
                display: flex;
                &.t-error {
                    .f-f-input {
                        border-color: base.$color-red;
                    }
                }
            }
                .c-wccpo--m-measurement {
                    flex: 1;
                    margin-right: base.unit-rem-calc(8px);
                    &:last-child {
                        margin-right: 0;
                    }
                }
            .c-wccpo--error {
                display: none;
                padding-top: base.unit-rem-calc(6px);
                font-size: base.unit-rem-calc(12px);
                color: base.$color-red;
            }
    }
}

.m-color-picker {
    z-index: 2000;
}

/******* Modal Overrides ********/
.s-modal {
    &.t-auth {
        width: base.unit-rem-calc(528px);
        @include base.respond-to('<640px') {
            width: 100%;
        }
    }
    &.t-manage-drawing {
        width: base.unit-rem-calc(528px);
        @include base.respond-to('<640px') {
            width: 100%;
        }
    }
    &.t-drawing-node-text {
        width: base.unit-rem-calc(528px);
        @include base.respond-to('<640px') {
            width: 100%;
        }
        .f-f-input {
            > textarea {
                height: base.unit-rem-calc(112px);
            }
        }
    }
    &.t-drawing-node-elevation {
        width: base.unit-rem-calc(350px);
    }
}
